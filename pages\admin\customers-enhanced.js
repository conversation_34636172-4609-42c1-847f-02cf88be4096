import { useState, useEffect } from 'react';
import { useRouter } from 'next/router';
import AdminLayout from '@/components/admin/AdminLayout';
import EnhancedCustomerList from '@/components/admin/EnhancedCustomerList';
import { authenticatedFetch } from '@/lib/auth-utils';
import styles from '@/styles/admin/CustomersEnhanced.module.css';

export default function CustomersEnhanced() {
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [stats, setStats] = useState({
    totalCustomers: 0,
    activeCustomers: 0,
    newThisMonth: 0,
    averageHealthScore: 0
  });

  useEffect(() => {
    fetchCustomerStats();
  }, []);

  const fetchCustomerStats = async () => {
    try {
      setLoading(true);
      
      // Fetch basic customer statistics
      const data = await authenticatedFetch('/api/admin/customers?limit=1');
      
      // For now, we'll use placeholder stats
      // In a real implementation, you'd have a dedicated stats endpoint
      setStats({
        totalCustomers: data?.length || 0,
        activeCustomers: data?.length || 0,
        newThisMonth: Math.floor((data?.length || 0) * 0.1),
        averageHealthScore: 75
      });
    } catch (error) {
      console.error('Error fetching customer stats:', error);
      setError('Failed to load customer statistics');
    } finally {
      setLoading(false);
    }
  };

  return (
    <AdminLayout>
      <div className={styles.customersEnhanced}>
        <div className={styles.header}>
          <div className={styles.titleSection}>
            <h1>Enhanced Customer Management</h1>
            <p className={styles.subtitle}>
              Advanced customer management with search, analytics, and insights
            </p>
          </div>
          
          <div className={styles.headerActions}>
            <button
              onClick={() => router.push('/admin/customers')}
              className={styles.switchViewButton}
            >
              Switch to Classic View
            </button>
          </div>
        </div>

        {/* Customer Statistics Dashboard */}
        <div className={styles.statsGrid}>
          <div className={styles.statCard}>
            <div className={styles.statIcon}>👥</div>
            <div className={styles.statContent}>
              <h3>Total Customers</h3>
              <div className={styles.statValue}>
                {loading ? '...' : stats.totalCustomers.toLocaleString()}
              </div>
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>✅</div>
            <div className={styles.statContent}>
              <h3>Active Customers</h3>
              <div className={styles.statValue}>
                {loading ? '...' : stats.activeCustomers.toLocaleString()}
              </div>
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>🆕</div>
            <div className={styles.statContent}>
              <h3>New This Month</h3>
              <div className={styles.statValue}>
                {loading ? '...' : stats.newThisMonth.toLocaleString()}
              </div>
            </div>
          </div>

          <div className={styles.statCard}>
            <div className={styles.statIcon}>💚</div>
            <div className={styles.statContent}>
              <h3>Avg Health Score</h3>
              <div className={styles.statValue}>
                {loading ? '...' : `${stats.averageHealthScore}%`}
              </div>
            </div>
          </div>
        </div>

        {error && (
          <div className={styles.error}>
            {error}
          </div>
        )}

        {/* Enhanced Customer List */}
        <div className={styles.customerListSection}>
          <EnhancedCustomerList />
        </div>

        {/* Feature Highlights */}
        <div className={styles.featuresSection}>
          <h2>New Features Available</h2>
          <div className={styles.featuresGrid}>
            <div className={styles.featureCard}>
              <h3>🔍 Advanced Search</h3>
              <p>Search customers by multiple criteria including tags, value, health score, and more.</p>
            </div>
            
            <div className={styles.featureCard}>
              <h3>🏷️ Customer Tags</h3>
              <p>Organize customers with custom tags and categories for better segmentation.</p>
            </div>
            
            <div className={styles.featureCard}>
              <h3>📊 Customer Analytics</h3>
              <p>View detailed analytics including health scores, churn risk, and engagement metrics.</p>
            </div>
            
            <div className={styles.featureCard}>
              <h3>⚡ Bulk Operations</h3>
              <p>Perform bulk actions on multiple customers including tag assignment and exports.</p>
            </div>
          </div>
        </div>
      </div>
    </AdminLayout>
  );
}

// Add authentication check
CustomersEnhanced.requireAuth = true;
