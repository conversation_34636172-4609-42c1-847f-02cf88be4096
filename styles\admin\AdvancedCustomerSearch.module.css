.searchContainer {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.quickSearch {
  margin-bottom: 20px;
}

.searchInputGroup {
  display: flex;
  gap: 10px;
  margin-bottom: 15px;
}

.searchInput {
  flex: 1;
  padding: 12px 16px;
  border: 1px solid #ddd;
  border-radius: 6px;
  font-size: 1rem;
  transition: border-color 0.2s ease;
}

.searchInput:focus {
  outline: none;
  border-color: #6e8efb;
  box-shadow: 0 0 0 3px rgba(110, 142, 251, 0.1);
}

.advancedToggle {
  padding: 12px 20px;
  background: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.advancedToggle:hover {
  background: #e9ecef;
}

.quickFilters {
  display: flex;
  gap: 15px;
  flex-wrap: wrap;
}

.quickSelect {
  padding: 10px 12px;
  border: 1px solid #ddd;
  border-radius: 6px;
  background: white;
  font-size: 0.95rem;
  cursor: pointer;
  transition: border-color 0.2s ease;
}

.quickSelect:focus {
  outline: none;
  border-color: #6e8efb;
}

.advancedFilters {
  border-top: 1px solid #eee;
  padding-top: 20px;
  margin-top: 20px;
}

.filterSection {
  margin-bottom: 25px;
}

.filterSection h4 {
  margin: 0 0 12px 0;
  font-size: 1rem;
  font-weight: 600;
  color: #333;
}

.rangeInputs {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.rangeInput {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  width: 140px;
  font-size: 0.9rem;
}

.rangeInput:focus {
  outline: none;
  border-color: #6e8efb;
}

.dateInputs {
  display: flex;
  align-items: center;
  gap: 10px;
  flex-wrap: wrap;
}

.dateInput {
  padding: 8px 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 0.9rem;
}

.dateInput:focus {
  outline: none;
  border-color: #6e8efb;
}

.tagsList {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.tagButton {
  padding: 6px 12px;
  border: 1px solid #ddd;
  border-radius: 20px;
  background: #f5f5f5;
  color: #333;
  font-size: 0.85rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tagButton:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.tagSelected {
  color: white !important;
  border-color: transparent !important;
  font-weight: 500;
}

.filterActions {
  display: flex;
  gap: 15px;
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.clearButton {
  padding: 10px 20px;
  background: #f8f9fa;
  border: 1px solid #ddd;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.clearButton:hover {
  background: #e9ecef;
}

.saveButton {
  padding: 10px 20px;
  background: #6e8efb;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.saveButton:hover {
  background: #5a7cfa;
}

.savedFilters {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #eee;
}

.savedFilters h4 {
  margin: 0 0 12px 0;
  font-size: 0.95rem;
  font-weight: 600;
  color: #666;
}

.savedFiltersList {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.savedFilterButton {
  padding: 6px 12px;
  background: #e3f2fd;
  color: #1976d2;
  border: 1px solid #bbdefb;
  border-radius: 4px;
  cursor: pointer;
  font-size: 0.85rem;
  transition: all 0.2s ease;
}

.savedFilterButton:hover {
  background: #bbdefb;
}

.activeFilters {
  margin-top: 15px;
  padding: 12px;
  background: #f8f9fa;
  border-radius: 6px;
  border-left: 4px solid #6e8efb;
}

.activeFiltersLabel {
  font-weight: 600;
  color: #333;
  margin-right: 10px;
}

.activeFilter {
  display: inline-block;
  background: white;
  color: #333;
  padding: 4px 8px;
  margin: 2px 4px;
  border-radius: 4px;
  font-size: 0.85rem;
  border: 1px solid #ddd;
}

.searchLoading {
  text-align: center;
  padding: 15px;
  color: #666;
  font-style: italic;
}

/* Responsive Design */
@media (max-width: 768px) {
  .searchContainer {
    padding: 15px;
  }
  
  .searchInputGroup {
    flex-direction: column;
  }
  
  .quickFilters {
    flex-direction: column;
  }
  
  .quickSelect {
    width: 100%;
  }
  
  .rangeInputs,
  .dateInputs {
    flex-direction: column;
    align-items: stretch;
  }
  
  .rangeInput,
  .dateInput {
    width: 100%;
  }
  
  .filterActions {
    flex-direction: column;
  }
  
  .clearButton,
  .saveButton {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .tagsList {
    justify-content: center;
  }
  
  .savedFiltersList {
    justify-content: center;
  }
}
