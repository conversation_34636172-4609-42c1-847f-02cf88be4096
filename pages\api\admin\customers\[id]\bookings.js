import { supabaseAdmin } from '@/lib/supabase';
import * as authTokenManager from '@/lib/auth-token-manager';

/**
 * Customer Bookings API
 * 
 * GET /api/admin/customers/{id}/bookings
 * 
 * Fetches all bookings for a specific customer with service details
 * Used by EnhancedBookingDetails component for customer history tab
 */
export default async function handler(req, res) {
  // Generate request ID for logging
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Customer bookings request started`);

  // Only allow GET requests
  if (req.method !== 'GET') {
    console.log(`[${requestId}] Method ${req.method} not allowed`);
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Verify authentication
    const authResult = await authTokenManager.verifyToken(req);
    if (!authResult.valid) {
      console.log(`[${requestId}] Authentication failed:`, authResult.error);
      return res.status(401).json({ error: 'Unauthorized' });
    }

    // Extract customer ID from URL
    const { id: customerId } = req.query;

    if (!customerId) {
      return res.status(400).json({ error: 'Customer ID is required' });
    }

    console.log(`[${requestId}] Fetching bookings for customer: ${customerId}`);

    // First verify the customer exists
    const { data: customer, error: customerError } = await supabaseAdmin
      .from('customers')
      .select('id, name, email')
      .eq('id', customerId)
      .single();

    if (customerError) {
      if (customerError.code === 'PGRST116') {
        return res.status(404).json({ error: 'Customer not found' });
      }
      console.error(`[${requestId}] Error fetching customer:`, customerError);
      throw customerError;
    }

    // Fetch customer's booking history with service details
    const { data: bookings, error: bookingsError } = await supabaseAdmin
      .from('bookings')
      .select(`
        id,
        start_time,
        end_time,
        status,
        location,
        notes,
        booking_reference,
        estimated_revenue,
        actual_revenue,
        priority_level,
        internal_notes,
        customer_notes,
        created_at,
        updated_at,
        services:service_id (
          id,
          name,
          color,
          price,
          duration
        )
      `)
      .eq('customer_id', customerId)
      .order('start_time', { ascending: false });

    if (bookingsError) {
      console.error(`[${requestId}] Error fetching bookings:`, bookingsError);
      throw bookingsError;
    }

    console.log(`[${requestId}] Found ${bookings?.length || 0} bookings for customer`);

    // Transform the data for frontend consumption
    const transformedBookings = bookings.map(booking => ({
      id: booking.id,
      start_time: booking.start_time,
      end_time: booking.end_time,
      status: booking.status,
      location: booking.location,
      notes: booking.notes,
      booking_reference: booking.booking_reference,
      estimated_revenue: booking.estimated_revenue,
      actual_revenue: booking.actual_revenue,
      priority_level: booking.priority_level,
      internal_notes: booking.internal_notes,
      customer_notes: booking.customer_notes,
      created_at: booking.created_at,
      updated_at: booking.updated_at,
      // Flatten service data
      serviceName: booking.services?.name || 'Unknown Service',
      serviceColor: booking.services?.color || '#3788d8',
      servicePrice: booking.services?.price || 0,
      serviceDuration: booking.services?.duration || 60,
      // Add computed fields
      revenue: booking.actual_revenue || booking.estimated_revenue || booking.services?.price || 0
    }));

    // Calculate customer statistics
    const stats = {
      totalBookings: bookings.length,
      confirmedBookings: bookings.filter(b => b.status === 'confirmed').length,
      canceledBookings: bookings.filter(b => b.status === 'canceled').length,
      completedBookings: bookings.filter(b => b.status === 'completed').length,
      totalRevenue: transformedBookings.reduce((sum, b) => sum + (b.revenue || 0), 0),
      averageRevenue: transformedBookings.length > 0 
        ? transformedBookings.reduce((sum, b) => sum + (b.revenue || 0), 0) / transformedBookings.length 
        : 0,
      lastBookingDate: bookings.length > 0 ? bookings[0].start_time : null,
      firstBookingDate: bookings.length > 0 ? bookings[bookings.length - 1].start_time : null
    };

    // Prepare response
    const response = {
      customer: {
        id: customer.id,
        name: customer.name,
        email: customer.email
      },
      bookings: transformedBookings,
      stats: stats,
      pagination: {
        total: bookings.length,
        limit: null, // No pagination for now, but can be added later
        offset: 0
      }
    };

    console.log(`[${requestId}] Customer bookings fetched successfully`);
    return res.status(200).json(response);

  } catch (error) {
    console.error(`[${requestId}] Error in customer bookings API:`, error);
    return res.status(500).json({ 
      error: 'Internal server error',
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while fetching customer bookings'
    });
  }
}
