/**
 * Auth Token Manager
 *
 * A centralized utility for managing authentication tokens
 * with a simplified, consistent approach to reduce complexity
 * and prevent authentication issues.
 *
 * This version uses Supabase directly for authentication.
 */

import supabase from './supabase';

// Constants
const TOKEN_STORAGE_KEY = 'oss_auth_token_cache';
const TOKEN_EXPIRY_BUFFER = 5 * 60 * 1000; // 5 minutes buffer before expiry
const COOKIE_TOKEN_KEY = 'oss_auth_token';
const SUPABASE_TOKEN_KEY = 'supabase_auth_token';

/**
 * Get the current auth token from Supabase session or storage
 *
 * @returns {Promise<string|null>} The auth token or null if not available
 */
export const getAuthToken = async () => {
  // First try to get token from sessionStorage for performance
  const cachedToken = getTokenFromStorage();
  if (cachedToken) {
    return cachedToken;
  }

  // If no valid token in storage, get from current Supabase session
  try {
    const { data, error } = await supabase.auth.getSession();

    if (error) {
      console.warn('Error getting session:', error);
      return null;
    }

    if (data?.session?.access_token) {
      // Store the token for future use
      storeToken(data.session.access_token);
      return data.session.access_token;
    }
  } catch (error) {
    console.warn('Failed to get session token:', error);
  }

  // If still no token, try to refresh the session
  try {
    const { data, error } = await supabase.auth.refreshSession();

    if (error) {
      console.error('Error refreshing session:', error);
      return null;
    }

    if (data?.session?.access_token) {
      // Store the refreshed token
      storeToken(data.session.access_token);
      return data.session.access_token;
    }
  } catch (error) {
    console.error('Error refreshing auth token:', error);
  }

  return null;
};

/**
 * Store a token in sessionStorage with expiration
 *
 * @param {string} token - The auth token to store
 */
export const storeToken = (token) => {
  if (!token || typeof window === 'undefined' || !window.sessionStorage) {
    return;
  }

  try {
    const tokenData = {
      token,
      expiry: Date.now() + (60 * 60 * 1000) - TOKEN_EXPIRY_BUFFER, // 1 hour minus buffer
      refreshed: Date.now()
    };

    sessionStorage.setItem(TOKEN_STORAGE_KEY, JSON.stringify(tokenData));
  } catch (error) {
    console.warn('Failed to store auth token:', error);
  }
};

/**
 * Store token in multiple locations for compatibility with documented architecture
 * This ensures the token is available where the server-side auth expects it
 *
 * @param {string} token - The auth token to store
 */
export const storeTokenInMultipleLocations = (token) => {
  if (!token || typeof window === 'undefined') {
    return;
  }

  console.log('[Auth Token Manager] Storing token in multiple locations for compatibility...');

  // Store in sessionStorage (primary location)
  storeToken(token);

  // Store in localStorage for compatibility with documented architecture
  try {
    localStorage.setItem(COOKIE_TOKEN_KEY, token);
    localStorage.setItem(SUPABASE_TOKEN_KEY, token);
    console.log('[Auth Token Manager] Stored token in localStorage');
  } catch (error) {
    console.warn('[Auth Token Manager] Failed to store token in localStorage:', error);
  }

  // Store in cookies for server-side authentication compatibility
  try {
    // Set cookie with appropriate security settings
    const isProduction = process.env.NODE_ENV === 'production';
    const cookieOptions = [
      `${COOKIE_TOKEN_KEY}=${token}`,
      'path=/',
      'SameSite=Lax',
      `max-age=${60 * 60}` // 1 hour
    ];

    if (isProduction) {
      cookieOptions.push('Secure');
    }

    document.cookie = cookieOptions.join('; ');
    console.log('[Auth Token Manager] Stored token in cookies');
  } catch (error) {
    console.warn('[Auth Token Manager] Failed to store token in cookies:', error);
  }
};

/**
 * Get a token from storage (checks multiple locations as per documented architecture)
 *
 * @returns {string|null} The auth token or null if not available/valid
 */
export const getTokenFromStorage = () => {
  if (typeof window === 'undefined') {
    return null;
  }

  // First try sessionStorage (primary location)
  try {
    const cachedToken = sessionStorage.getItem(TOKEN_STORAGE_KEY);
    if (cachedToken) {
      const tokenData = JSON.parse(cachedToken);
      if (tokenData && tokenData.token && tokenData.expiry > Date.now()) {
        console.log('[Auth Token Manager] Found valid token in sessionStorage');
        return tokenData.token;
      }
    }
  } catch (error) {
    console.warn('[Auth Token Manager] Error accessing token from sessionStorage:', error);
  }

  // Try localStorage (compatibility with documented architecture)
  try {
    const localToken = localStorage.getItem(COOKIE_TOKEN_KEY) || localStorage.getItem(SUPABASE_TOKEN_KEY);
    if (localToken) {
      console.log('[Auth Token Manager] Found token in localStorage');

      // Check if the token is a JSON object (common with Supabase storage)
      if (localToken.startsWith('{')) {
        try {
          const parsedToken = JSON.parse(localToken);
          // Extract access_token from the session object
          if (parsedToken.access_token) {
            console.log('[Auth Token Manager] Extracted access_token from JSON object');
            return parsedToken.access_token;
          }
          // Handle nested session structure
          if (parsedToken.session && parsedToken.session.access_token) {
            console.log('[Auth Token Manager] Extracted access_token from nested session object');
            return parsedToken.session.access_token;
          }
        } catch (parseError) {
          console.warn('[Auth Token Manager] Failed to parse JSON token from localStorage:', parseError);
        }
      }

      // Return the token as-is if it's not JSON
      return localToken;
    }
  } catch (error) {
    console.warn('[Auth Token Manager] Error accessing token from localStorage:', error);
  }

  // Try cookies (server-side compatibility)
  try {
    const cookies = document.cookie.split(';');
    for (let cookie of cookies) {
      const [name, value] = cookie.trim().split('=');
      if (name === COOKIE_TOKEN_KEY && value) {
        console.log('[Auth Token Manager] Found token in cookies');
        return value;
      }
    }
  } catch (error) {
    console.warn('[Auth Token Manager] Error accessing token from cookies:', error);
  }

  return null;
};

/**
 * Clear the auth token from all storage locations
 */
export const clearToken = () => {
  if (typeof window === 'undefined') {
    return;
  }

  console.log('[Auth Token Manager] Clearing tokens from all locations...');

  // Clear from sessionStorage
  try {
    sessionStorage.removeItem(TOKEN_STORAGE_KEY);
    console.log('[Auth Token Manager] Cleared token from sessionStorage');
  } catch (error) {
    console.warn('[Auth Token Manager] Error clearing token from sessionStorage:', error);
  }

  // Clear from localStorage
  try {
    localStorage.removeItem(COOKIE_TOKEN_KEY);
    localStorage.removeItem(SUPABASE_TOKEN_KEY);
    console.log('[Auth Token Manager] Cleared token from localStorage');
  } catch (error) {
    console.warn('[Auth Token Manager] Error clearing token from localStorage:', error);
  }

  // Clear from cookies
  try {
    document.cookie = `${COOKIE_TOKEN_KEY}=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT`;
    console.log('[Auth Token Manager] Cleared token from cookies');
  } catch (error) {
    console.warn('[Auth Token Manager] Error clearing token from cookies:', error);
  }
};

/**
 * Add auth headers to a request options object
 *
 * @param {Object} options - The request options
 * @returns {Promise<Object>} The updated options with auth headers
 */
export const addAuthHeaders = async (options = {}) => {
  const token = await getAuthToken();

  const headers = {
    ...options.headers,
    'Content-Type': 'application/json',
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  return {
    ...options,
    headers,
    credentials: 'include', // Always include credentials for cookies
  };
};

/**
 * Verify authentication token from request
 * Used by API endpoints for server-side authentication
 *
 * @param {Object} req - The request object
 * @returns {Promise<Object>} Authentication result with user info
 */
export const verifyToken = async (req) => {
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Token verification started`);

  try {
    // Extract token from request headers
    let token = null;

    // Try Authorization header first
    const authHeader = req.headers.authorization;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      token = authHeader.substring(7);
      console.log(`[${requestId}] Token found in Authorization header, length: ${token.length}, starts with: ${token.substring(0, 50)}...`);

      // Check if the token is a JSON object (sometimes happens with frontend storage issues)
      if (token.startsWith('{')) {
        console.log(`[${requestId}] Token appears to be JSON object, attempting to parse...`);
        try {
          const parsedToken = JSON.parse(token);
          // Extract access_token from the session object
          if (parsedToken.access_token) {
            console.log(`[${requestId}] Extracted access_token from JSON object in Authorization header`);
            token = parsedToken.access_token;
          }
          // Handle nested session structure
          else if (parsedToken.session && parsedToken.session.access_token) {
            console.log(`[${requestId}] Extracted access_token from nested session object in Authorization header`);
            token = parsedToken.session.access_token;
          }
          else {
            console.warn(`[${requestId}] JSON token does not contain access_token or session.access_token`);
          }
        } catch (parseError) {
          console.warn(`[${requestId}] Failed to parse JSON token from Authorization header:`, parseError);
        }
      }
    }

    // Try X-Auth-Token header as fallback
    if (!token && req.headers['x-auth-token']) {
      token = req.headers['x-auth-token'];
      console.log(`[${requestId}] Token found in X-Auth-Token header`);
    }

    // Try cookies as final fallback
    if (!token && req.cookies && req.cookies[COOKIE_TOKEN_KEY]) {
      token = req.cookies[COOKIE_TOKEN_KEY];
      console.log(`[${requestId}] Token found in cookies`);
    }

    if (!token) {
      console.log(`[${requestId}] No authentication token found`);
      return {
        valid: false,
        user: null,
        error: 'No authentication token provided'
      };
    }

    // Verify token with Supabase admin client
    const { supabaseAdmin } = await import('./supabase');
    if (!supabaseAdmin) {
      console.error(`[${requestId}] Supabase admin client not available`);
      return {
        valid: false,
        user: null,
        error: 'Authentication service unavailable'
      };
    }

    const { data, error } = await supabaseAdmin.auth.getUser(token);

    if (error || !data.user) {
      console.log(`[${requestId}] Token verification failed:`, error?.message);
      return {
        valid: false,
        user: null,
        error: error?.message || 'Invalid authentication token'
      };
    }

    // Get user role from user metadata or database
    let role = 'user'; // default role
    if (data.user.user_metadata?.role) {
      role = data.user.user_metadata.role;
    } else if (data.user.app_metadata?.role) {
      role = data.user.app_metadata.role;
    } else {
      // Try to get role from database
      try {
        const { data: roleData, error: roleError } = await supabaseAdmin
          .from('user_roles')
          .select('role')
          .eq('id', data.user.id)
          .single();

        if (!roleError && roleData) {
          role = roleData.role;
        }
      } catch (roleQueryError) {
        console.warn(`[${requestId}] Could not fetch user role from database:`, roleQueryError);
      }
    }

    console.log(`[${requestId}] Token verification successful for user: ${data.user.email}`);
    return {
      valid: true,
      user: {
        id: data.user.id,
        email: data.user.email,
        role: role
      },
      error: null
    };

  } catch (error) {
    console.error(`[${requestId}] Token verification error:`, error);
    return {
      valid: false,
      user: null,
      error: 'Authentication verification failed'
    };
  }
};

export default {
  getAuthToken,
  storeToken,
  storeTokenInMultipleLocations,
  getTokenFromStorage,
  clearToken,
  addAuthHeaders,
  verifyToken
};
