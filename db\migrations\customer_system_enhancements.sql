-- Customer Management System Enhancements Migration
-- This script adds new tables and columns to support enhanced customer management

-- =============================================
-- ENHANCED CUSTOMERS TABLE
-- =============================================

-- Add new columns to existing customers table
ALTER TABLE public.customers 
ADD COLUMN IF NOT EXISTS customer_health_score INTEGER DEFAULT 50 CHECK (customer_health_score >= 0 AND customer_health_score <= 100),
ADD COLUMN IF NOT EXISTS churn_risk_score DECIMAL(3,2) DEFAULT 0.0 CHECK (churn_risk_score >= 0.0 AND churn_risk_score <= 1.0),
ADD COLUMN IF NOT EXISTS preferred_communication_method TEXT DEFAULT 'email' CHECK (preferred_communication_method IN ('email', 'sms', 'phone', 'in_person')),
ADD COLUMN IF NOT EXISTS communication_frequency TEXT DEFAULT 'normal' CHECK (communication_frequency IN ('minimal', 'normal', 'frequent')),
ADD COLUMN IF NOT EXISTS last_communication_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS acquisition_source TEXT,
ADD COLUMN IF NOT EXISTS acquisition_date DATE DEFAULT CURRENT_DATE,
ADD COLUMN IF NOT EXISTS referral_customer_id UUID REFERENCES public.customers(id),
ADD COLUMN IF NOT EXISTS customer_status TEXT DEFAULT 'active' CHECK (customer_status IN ('active', 'inactive', 'suspended', 'churned')),
ADD COLUMN IF NOT EXISTS satisfaction_score DECIMAL(3,2) CHECK (satisfaction_score >= 0.0 AND satisfaction_score <= 5.0),
ADD COLUMN IF NOT EXISTS last_satisfaction_date TIMESTAMPTZ,
ADD COLUMN IF NOT EXISTS customer_tier TEXT DEFAULT 'bronze' CHECK (customer_tier IN ('bronze', 'silver', 'gold', 'platinum')),
ADD COLUMN IF NOT EXISTS tier_since TIMESTAMPTZ DEFAULT NOW(),
ADD COLUMN IF NOT EXISTS next_tier_threshold DECIMAL(10,2);

-- Create indexes for improved performance
CREATE INDEX IF NOT EXISTS customers_health_score_idx ON public.customers(customer_health_score);
CREATE INDEX IF NOT EXISTS customers_churn_risk_idx ON public.customers(churn_risk_score);
CREATE INDEX IF NOT EXISTS customers_status_idx ON public.customers(customer_status);
CREATE INDEX IF NOT EXISTS customers_tier_idx ON public.customers(customer_tier);
CREATE INDEX IF NOT EXISTS customers_acquisition_idx ON public.customers(acquisition_source, acquisition_date);
CREATE INDEX IF NOT EXISTS customers_communication_idx ON public.customers(preferred_communication_method, last_communication_date);
CREATE INDEX IF NOT EXISTS customers_referral_idx ON public.customers(referral_customer_id);

-- Enhanced full-text search index
CREATE INDEX IF NOT EXISTS customers_full_text_search_idx ON public.customers 
USING gin(to_tsvector('english', 
  name || ' ' || 
  email || ' ' || 
  COALESCE(phone, '') || ' ' || 
  COALESCE(notes, '') || ' ' ||
  COALESCE(city, '') || ' ' ||
  COALESCE(state, '')
));

-- =============================================
-- CUSTOMER TAGS SYSTEM
-- =============================================

-- Customer tags table
CREATE TABLE IF NOT EXISTS public.customer_tags (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL UNIQUE,
  description TEXT,
  color TEXT NOT NULL DEFAULT '#6e8efb',
  is_system BOOLEAN DEFAULT FALSE,
  category TEXT DEFAULT 'general',
  sort_order INTEGER DEFAULT 0,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Customer tag assignments table
CREATE TABLE IF NOT EXISTS public.customer_tag_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
  tag_id UUID REFERENCES public.customer_tags(id) ON DELETE CASCADE,
  assigned_by UUID REFERENCES auth.users(id),
  assigned_at TIMESTAMPTZ DEFAULT NOW(),
  notes TEXT,
  UNIQUE(customer_id, tag_id)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS customer_tags_name_idx ON public.customer_tags(name);
CREATE INDEX IF NOT EXISTS customer_tags_category_idx ON public.customer_tags(category);
CREATE INDEX IF NOT EXISTS customer_tag_assignments_customer_idx ON public.customer_tag_assignments(customer_id);
CREATE INDEX IF NOT EXISTS customer_tag_assignments_tag_idx ON public.customer_tag_assignments(tag_id);

-- =============================================
-- CUSTOMER SEGMENTS SYSTEM
-- =============================================

-- Customer segments table
CREATE TABLE IF NOT EXISTS public.customer_segments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  criteria JSONB NOT NULL,
  is_dynamic BOOLEAN DEFAULT TRUE,
  is_system BOOLEAN DEFAULT FALSE,
  customer_count INTEGER DEFAULT 0,
  last_calculated TIMESTAMPTZ,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Customer segment assignments table (for static segments)
CREATE TABLE IF NOT EXISTS public.customer_segment_assignments (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
  segment_id UUID REFERENCES public.customer_segments(id) ON DELETE CASCADE,
  assigned_by UUID REFERENCES auth.users(id),
  assigned_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(customer_id, segment_id)
);

-- Create indexes
CREATE INDEX IF NOT EXISTS customer_segments_dynamic_idx ON public.customer_segments(is_dynamic);
CREATE INDEX IF NOT EXISTS customer_segment_assignments_customer_idx ON public.customer_segment_assignments(customer_id);
CREATE INDEX IF NOT EXISTS customer_segment_assignments_segment_idx ON public.customer_segment_assignments(segment_id);

-- =============================================
-- CUSTOMER COMMUNICATIONS SYSTEM
-- =============================================

-- Customer communications table
CREATE TABLE IF NOT EXISTS public.customer_communications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
  communication_type TEXT NOT NULL CHECK (communication_type IN ('email', 'sms', 'phone', 'in_person', 'system', 'chat')),
  direction TEXT NOT NULL CHECK (direction IN ('inbound', 'outbound')),
  subject TEXT,
  content TEXT NOT NULL,
  status TEXT DEFAULT 'sent' CHECK (status IN ('pending', 'sent', 'delivered', 'failed', 'read', 'replied')),
  sent_by UUID REFERENCES auth.users(id),
  external_id TEXT,
  metadata JSONB,
  scheduled_for TIMESTAMPTZ,
  sent_at TIMESTAMPTZ,
  read_at TIMESTAMPTZ,
  replied_at TIMESTAMPTZ,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS customer_communications_customer_idx ON public.customer_communications(customer_id);
CREATE INDEX IF NOT EXISTS customer_communications_type_idx ON public.customer_communications(communication_type);
CREATE INDEX IF NOT EXISTS customer_communications_status_idx ON public.customer_communications(status);
CREATE INDEX IF NOT EXISTS customer_communications_date_idx ON public.customer_communications(created_at);
CREATE INDEX IF NOT EXISTS customer_communications_scheduled_idx ON public.customer_communications(scheduled_for) WHERE scheduled_for IS NOT NULL;

-- =============================================
-- CUSTOMER JOURNEY EVENTS
-- =============================================

-- Customer journey events table
CREATE TABLE IF NOT EXISTS public.customer_journey_events (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
  event_type TEXT NOT NULL,
  event_category TEXT DEFAULT 'general',
  event_data JSONB,
  source TEXT,
  user_agent TEXT,
  ip_address INET,
  session_id TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS customer_journey_events_customer_idx ON public.customer_journey_events(customer_id);
CREATE INDEX IF NOT EXISTS customer_journey_events_type_idx ON public.customer_journey_events(event_type);
CREATE INDEX IF NOT EXISTS customer_journey_events_category_idx ON public.customer_journey_events(event_category);
CREATE INDEX IF NOT EXISTS customer_journey_events_date_idx ON public.customer_journey_events(created_at);
CREATE INDEX IF NOT EXISTS customer_journey_events_session_idx ON public.customer_journey_events(session_id);

-- =============================================
-- CUSTOMER LOYALTY SYSTEM
-- =============================================

-- Customer loyalty table
CREATE TABLE IF NOT EXISTS public.customer_loyalty (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
  tier TEXT NOT NULL DEFAULT 'bronze' CHECK (tier IN ('bronze', 'silver', 'gold', 'platinum')),
  points INTEGER DEFAULT 0,
  lifetime_points INTEGER DEFAULT 0,
  tier_since TIMESTAMPTZ DEFAULT NOW(),
  next_tier_points INTEGER,
  points_expiry_date DATE,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  UNIQUE(customer_id)
);

-- Customer loyalty transactions table
CREATE TABLE IF NOT EXISTS public.customer_loyalty_transactions (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  customer_id UUID REFERENCES public.customers(id) ON DELETE CASCADE,
  transaction_type TEXT NOT NULL CHECK (transaction_type IN ('earned', 'redeemed', 'expired', 'adjusted')),
  points INTEGER NOT NULL,
  description TEXT,
  reference_type TEXT, -- 'booking', 'order', 'referral', 'manual'
  reference_id UUID,
  created_by UUID REFERENCES auth.users(id),
  created_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes
CREATE INDEX IF NOT EXISTS customer_loyalty_customer_idx ON public.customer_loyalty(customer_id);
CREATE INDEX IF NOT EXISTS customer_loyalty_tier_idx ON public.customer_loyalty(tier);
CREATE INDEX IF NOT EXISTS customer_loyalty_transactions_customer_idx ON public.customer_loyalty_transactions(customer_id);
CREATE INDEX IF NOT EXISTS customer_loyalty_transactions_type_idx ON public.customer_loyalty_transactions(transaction_type);
CREATE INDEX IF NOT EXISTS customer_loyalty_transactions_date_idx ON public.customer_loyalty_transactions(created_at);

-- =============================================
-- CUSTOMER ANALYTICS VIEWS
-- =============================================

-- Customer analytics summary view
CREATE OR REPLACE VIEW public.customer_analytics_summary AS
SELECT 
  c.id,
  c.name,
  c.email,
  c.customer_status,
  c.customer_tier,
  c.lifetime_value,
  c.booking_count,
  c.customer_health_score,
  c.churn_risk_score,
  c.created_at as registration_date,
  c.last_booking_date,
  c.acquisition_source,
  
  -- Booking metrics
  COUNT(DISTINCT b.id) as total_bookings,
  COUNT(DISTINCT CASE WHEN b.status = 'completed' THEN b.id END) as completed_bookings,
  COUNT(DISTINCT CASE WHEN b.status = 'canceled' THEN b.id END) as canceled_bookings,
  COALESCE(SUM(CASE WHEN b.status = 'completed' THEN COALESCE(b.actual_revenue, b.estimated_revenue, 0) END), 0) as total_revenue,
  
  -- Communication metrics
  COUNT(DISTINCT cc.id) as total_communications,
  COUNT(DISTINCT CASE WHEN cc.direction = 'outbound' THEN cc.id END) as outbound_communications,
  COUNT(DISTINCT CASE WHEN cc.direction = 'inbound' THEN cc.id END) as inbound_communications,
  
  -- Loyalty metrics
  COALESCE(cl.points, 0) as loyalty_points,
  COALESCE(cl.lifetime_points, 0) as lifetime_loyalty_points,
  
  -- Engagement metrics
  EXTRACT(DAYS FROM NOW() - c.last_booking_date) as days_since_last_booking,
  EXTRACT(DAYS FROM NOW() - c.last_communication_date) as days_since_last_communication,
  
  -- Tags
  ARRAY_AGG(DISTINCT ct.name) FILTER (WHERE ct.name IS NOT NULL) as tags

FROM public.customers c
LEFT JOIN public.bookings b ON c.id = b.customer_id
LEFT JOIN public.customer_communications cc ON c.id = cc.customer_id
LEFT JOIN public.customer_loyalty cl ON c.id = cl.customer_id
LEFT JOIN public.customer_tag_assignments cta ON c.id = cta.customer_id
LEFT JOIN public.customer_tags ct ON cta.tag_id = ct.id
GROUP BY c.id, c.name, c.email, c.customer_status, c.customer_tier, c.lifetime_value, 
         c.booking_count, c.customer_health_score, c.churn_risk_score, c.created_at,
         c.last_booking_date, c.acquisition_source, c.last_communication_date,
         cl.points, cl.lifetime_points;

-- Customer segmentation view
CREATE OR REPLACE VIEW public.customer_segments_view AS
SELECT 
  'new_customers' as segment_id,
  'New Customers' as segment_name,
  'Customers registered in the last 30 days' as description,
  COUNT(*) as customer_count
FROM public.customers 
WHERE created_at >= NOW() - INTERVAL '30 days'

UNION ALL

SELECT 
  'regular_customers' as segment_id,
  'Regular Customers' as segment_name,
  'Customers with 3+ bookings' as description,
  COUNT(*) as customer_count
FROM public.customers 
WHERE booking_count >= 3

UNION ALL

SELECT 
  'vip_customers' as segment_id,
  'VIP Customers' as segment_name,
  'High-value customers' as description,
  COUNT(*) as customer_count
FROM public.customers 
WHERE vip = TRUE OR lifetime_value > 1000

UNION ALL

SELECT 
  'inactive_customers' as segment_id,
  'Inactive Customers' as segment_name,
  'No bookings in the last 90 days' as description,
  COUNT(*) as customer_count
FROM public.customers 
WHERE last_booking_date < NOW() - INTERVAL '90 days' OR last_booking_date IS NULL;

-- =============================================
-- FUNCTIONS AND TRIGGERS
-- =============================================

-- Function to calculate customer health score
CREATE OR REPLACE FUNCTION public.calculate_customer_health_score(customer_id UUID)
RETURNS INTEGER AS $$
DECLARE
  score INTEGER := 50;
  booking_count INTEGER;
  days_since_last_booking INTEGER;
  lifetime_value DECIMAL;
  communication_responsiveness DECIMAL;
BEGIN
  -- Get customer metrics
  SELECT 
    c.booking_count,
    EXTRACT(DAYS FROM NOW() - c.last_booking_date),
    c.lifetime_value
  INTO booking_count, days_since_last_booking, lifetime_value
  FROM public.customers c
  WHERE c.id = customer_id;
  
  -- Booking frequency score (0-30 points)
  IF booking_count >= 10 THEN
    score := score + 30;
  ELSIF booking_count >= 5 THEN
    score := score + 20;
  ELSIF booking_count >= 2 THEN
    score := score + 10;
  END IF;
  
  -- Recency score (0-25 points)
  IF days_since_last_booking IS NULL THEN
    score := score - 25;
  ELSIF days_since_last_booking <= 30 THEN
    score := score + 25;
  ELSIF days_since_last_booking <= 60 THEN
    score := score + 15;
  ELSIF days_since_last_booking <= 90 THEN
    score := score + 5;
  ELSE
    score := score - 15;
  END IF;
  
  -- Value score (0-25 points)
  IF lifetime_value >= 1000 THEN
    score := score + 25;
  ELSIF lifetime_value >= 500 THEN
    score := score + 15;
  ELSIF lifetime_value >= 200 THEN
    score := score + 10;
  ELSIF lifetime_value >= 100 THEN
    score := score + 5;
  END IF;
  
  -- Ensure score is within bounds
  score := GREATEST(0, LEAST(100, score));
  
  RETURN score;
END;
$$ LANGUAGE plpgsql;

-- Function to update customer metrics
CREATE OR REPLACE FUNCTION public.update_customer_metrics()
RETURNS TRIGGER AS $$
BEGIN
  -- Update customer health score
  UPDATE public.customers 
  SET customer_health_score = public.calculate_customer_health_score(NEW.customer_id)
  WHERE id = NEW.customer_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Trigger to update customer metrics when bookings change
DROP TRIGGER IF EXISTS update_customer_metrics_trigger ON public.bookings;
CREATE TRIGGER update_customer_metrics_trigger
  AFTER INSERT OR UPDATE OR DELETE ON public.bookings
  FOR EACH ROW
  EXECUTE FUNCTION public.update_customer_metrics();

-- =============================================
-- ROW LEVEL SECURITY POLICIES
-- =============================================

-- Enable RLS on new tables
ALTER TABLE public.customer_tags ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_tag_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_segments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_segment_assignments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_communications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_journey_events ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_loyalty ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.customer_loyalty_transactions ENABLE ROW LEVEL SECURITY;

-- Policies for customer_tags
CREATE POLICY "Staff can manage customer tags" ON public.customer_tags
  FOR ALL USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- Policies for customer_tag_assignments
CREATE POLICY "Staff can manage customer tag assignments" ON public.customer_tag_assignments
  FOR ALL USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- Policies for customer_communications
CREATE POLICY "Staff can manage customer communications" ON public.customer_communications
  FOR ALL USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- Similar policies for other tables
CREATE POLICY "Staff can manage customer segments" ON public.customer_segments
  FOR ALL USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Staff can manage customer journey events" ON public.customer_journey_events
  FOR ALL USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

CREATE POLICY "Staff can manage customer loyalty" ON public.customer_loyalty
  FOR ALL USING (
    auth.role() IN ('service_role', 'supabase_admin') OR
    EXISTS (
      SELECT 1 FROM auth.users
      WHERE auth.users.id = auth.uid() AND auth.users.role IN ('admin', 'staff')
    )
  );

-- =============================================
-- SAMPLE DATA (Optional)
-- =============================================

-- Insert default customer tags
INSERT INTO public.customer_tags (name, description, color, is_system, category) VALUES
('VIP', 'Very Important Person', '#FFD700', TRUE, 'status'),
('New Customer', 'Recently registered customer', '#4CAF50', TRUE, 'status'),
('High Value', 'Customer with high lifetime value', '#9C27B0', TRUE, 'value'),
('Frequent Booker', 'Books services regularly', '#2196F3', TRUE, 'behavior'),
('At Risk', 'May churn soon', '#F44336', TRUE, 'risk'),
('Referral Source', 'Refers other customers', '#FF9800', TRUE, 'behavior')
ON CONFLICT (name) DO NOTHING;

-- Insert default customer segments
INSERT INTO public.customer_segments (name, description, criteria, is_dynamic, is_system) VALUES
('New Customers', 'Customers registered in the last 30 days', 
 '{"registration_days": 30}', TRUE, TRUE),
('Regular Customers', 'Customers with 3 or more bookings', 
 '{"min_bookings": 3}', TRUE, TRUE),
('VIP Customers', 'High-value customers', 
 '{"min_lifetime_value": 1000}', TRUE, TRUE),
('Inactive Customers', 'No bookings in the last 90 days', 
 '{"inactive_days": 90}', TRUE, TRUE)
ON CONFLICT DO NOTHING;

-- =============================================
-- PERFORMANCE OPTIMIZATIONS
-- =============================================

-- Create composite indexes for common queries
CREATE INDEX IF NOT EXISTS customers_search_composite_idx 
ON public.customers(customer_status, customer_tier, lifetime_value, booking_count);

CREATE INDEX IF NOT EXISTS customers_engagement_idx 
ON public.customers(last_booking_date, last_communication_date, customer_health_score);

CREATE INDEX IF NOT EXISTS customer_communications_timeline_idx 
ON public.customer_communications(customer_id, created_at DESC);

-- Analyze tables for query optimization
ANALYZE public.customers;
ANALYZE public.customer_tags;
ANALYZE public.customer_tag_assignments;
ANALYZE public.customer_segments;
ANALYZE public.customer_communications;
ANALYZE public.customer_journey_events;
ANALYZE public.customer_loyalty;
