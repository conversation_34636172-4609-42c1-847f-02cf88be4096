import { supabaseAdmin } from '@/lib/supabase';
import * as authTokenManager from '@/lib/auth-token-manager';

/**
 * Advanced Customer Search API Endpoint
 * Provides comprehensive search and filtering capabilities for customers
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Advanced customer search API: ${req.method} ${req.url}`);

  // Only allow GET requests
  if (req.method !== 'GET') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  // Authenticate request using the auth token manager
  const authResult = await authTokenManager.verifyToken(req);

  if (!authResult.valid) {
    console.error(`[${requestId}] Authentication failed:`, authResult.error);
    return res.status(401).json({
      error: 'Unauthorized access',
      message: authResult.error || 'Authentication failed',
      requestId
    });
  }

  console.log(`[${requestId}] Authentication successful. User: ${authResult.user?.email}`);

  try {
    // Extract query parameters
    const {
      search = '',
      tags = [],
      segment = 'all',
      lifetimeValue = {},
      bookingCount = {},
      location = {},
      customerStatus = 'all',
      customerTier = 'all',
      acquisitionSource = '',
      registrationDate = {},
      lastBookingDate = {},
      healthScore = {},
      churnRisk = {},
      limit = 50,
      offset = 0,
      sortBy = 'created_at',
      sortOrder = 'desc'
    } = req.query;

    console.log(`[${requestId}] Search parameters:`, {
      search, segment, customerStatus, limit, offset
    });

    // Start building the query using the analytics summary view for better performance
    let query = supabaseAdmin
      .from('customer_analytics_summary')
      .select('*', { count: 'exact' });

    // Apply text search filter
    if (search && search.trim()) {
      // Use the full-text search index for better performance
      query = query.or(`name.ilike.%${search}%,email.ilike.%${search}%`);
    }

    // Apply segment filter
    if (segment !== 'all') {
      switch (segment) {
        case 'new':
          // Customers registered in the last 30 days
          const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString();
          query = query.gte('registration_date', thirtyDaysAgo);
          break;
        case 'regular':
          // Customers with 3+ bookings
          query = query.gte('total_bookings', 3);
          break;
        case 'vip':
          // High-value customers
          query = query.or('customer_tier.eq.gold,customer_tier.eq.platinum,lifetime_value.gte.1000');
          break;
        case 'inactive':
          // No bookings in the last 90 days
          const ninetyDaysAgo = new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString();
          query = query.or(`last_booking_date.lt.${ninetyDaysAgo},last_booking_date.is.null`);
          break;
        case 'high_value':
          query = query.gte('lifetime_value', 1000);
          break;
        case 'at_risk':
          query = query.gte('churn_risk_score', 0.7);
          break;
      }
    }

    // Apply customer status filter
    if (customerStatus !== 'all') {
      query = query.eq('customer_status', customerStatus);
    }

    // Apply customer tier filter
    if (customerTier !== 'all') {
      query = query.eq('customer_tier', customerTier);
    }

    // Apply lifetime value filters
    if (lifetimeValue.min) {
      query = query.gte('lifetime_value', parseFloat(lifetimeValue.min));
    }
    if (lifetimeValue.max) {
      query = query.lte('lifetime_value', parseFloat(lifetimeValue.max));
    }

    // Apply booking count filters
    if (bookingCount.min) {
      query = query.gte('total_bookings', parseInt(bookingCount.min));
    }
    if (bookingCount.max) {
      query = query.lte('total_bookings', parseInt(bookingCount.max));
    }

    // Apply health score filters
    if (healthScore.min) {
      query = query.gte('customer_health_score', parseInt(healthScore.min));
    }
    if (healthScore.max) {
      query = query.lte('customer_health_score', parseInt(healthScore.max));
    }

    // Apply churn risk filters
    if (churnRisk.min) {
      query = query.gte('churn_risk_score', parseFloat(churnRisk.min));
    }
    if (churnRisk.max) {
      query = query.lte('churn_risk_score', parseFloat(churnRisk.max));
    }

    // Apply acquisition source filter
    if (acquisitionSource && acquisitionSource !== 'all') {
      query = query.eq('acquisition_source', acquisitionSource);
    }

    // Apply registration date filters
    if (registrationDate.start) {
      query = query.gte('registration_date', registrationDate.start);
    }
    if (registrationDate.end) {
      query = query.lte('registration_date', registrationDate.end);
    }

    // Apply last booking date filters
    if (lastBookingDate.start) {
      query = query.gte('last_booking_date', lastBookingDate.start);
    }
    if (lastBookingDate.end) {
      query = query.lte('last_booking_date', lastBookingDate.end);
    }

    // Apply tag filters
    if (tags && Array.isArray(tags) && tags.length > 0) {
      // Filter customers that have any of the specified tags
      const tagConditions = tags.map(tag => `tags.cs.{${tag}}`).join(',');
      query = query.or(tagConditions);
    }

    // Apply sorting
    const validSortColumns = [
      'name', 'email', 'registration_date', 'last_booking_date', 
      'lifetime_value', 'total_bookings', 'customer_health_score', 
      'churn_risk_score', 'customer_tier'
    ];
    
    if (validSortColumns.includes(sortBy)) {
      const ascending = sortOrder === 'asc';
      query = query.order(sortBy, { ascending });
    } else {
      // Default sort
      query = query.order('registration_date', { ascending: false });
    }

    // Apply pagination
    const limitNum = Math.min(parseInt(limit) || 50, 100); // Max 100 results
    const offsetNum = parseInt(offset) || 0;
    
    query = query.range(offsetNum, offsetNum + limitNum - 1);

    // Execute the query
    const { data, error, count } = await query;

    if (error) {
      console.error(`[${requestId}] Error executing search query:`, error);
      throw error;
    }

    console.log(`[${requestId}] Search completed. Found ${count} total customers, returning ${data?.length || 0}`);

    // Return the results
    return res.status(200).json({
      customers: data || [],
      total: count || 0,
      limit: limitNum,
      offset: offsetNum,
      hasMore: (offsetNum + limitNum) < (count || 0),
      requestId
    });

  } catch (error) {
    console.error(`[${requestId}] Customer search error:`, error);
    
    return res.status(500).json({
      error: 'Failed to search customers',
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while searching customers',
      requestId,
      timestamp: new Date().toISOString()
    });
  }
}
