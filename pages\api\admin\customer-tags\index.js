import { supabaseAdmin } from '@/lib/supabase';
import * as authTokenManager from '@/lib/auth-token-manager';

/**
 * Customer Tags Management API Endpoint
 * Handles CRUD operations for customer tags
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Customer tags API: ${req.method} ${req.url}`);

  // Authenticate request using the auth token manager
  const authResult = await authTokenManager.verifyToken(req);

  if (!authResult.valid) {
    console.error(`[${requestId}] Authentication failed:`, authResult.error);
    return res.status(401).json({
      error: 'Unauthorized access',
      message: authResult.error || 'Authentication failed',
      requestId
    });
  }

  console.log(`[${requestId}] Authentication successful. User: ${authResult.user?.email}`);

  try {
    if (req.method === 'GET') {
      // GET - List all tags
      console.log(`[${requestId}] Fetching all customer tags`);

      const { category, includeSystem = 'true' } = req.query;

      let query = supabaseAdmin
        .from('customer_tags')
        .select('*')
        .order('category', { ascending: true })
        .order('sort_order', { ascending: true })
        .order('name', { ascending: true });

      // Filter by category if specified
      if (category && category !== 'all') {
        query = query.eq('category', category);
      }

      // Filter system tags if specified
      if (includeSystem === 'false') {
        query = query.eq('is_system', false);
      }

      const { data, error } = await query;

      if (error) {
        console.error(`[${requestId}] Error fetching tags:`, error);
        throw error;
      }

      console.log(`[${requestId}] Successfully fetched ${data?.length || 0} tags`);

      return res.status(200).json({
        tags: data || [],
        requestId
      });

    } else if (req.method === 'POST') {
      // POST - Create new tag
      console.log(`[${requestId}] Creating new customer tag`);

      const { 
        name, 
        description, 
        color = '#6e8efb', 
        category = 'general',
        sortOrder = 0
      } = req.body;

      // Validate required fields
      if (!name || !name.trim()) {
        return res.status(400).json({ 
          error: 'Tag name is required',
          requestId 
        });
      }

      // Check if tag name already exists
      const { data: existingTag, error: checkError } = await supabaseAdmin
        .from('customer_tags')
        .select('id')
        .eq('name', name.trim())
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        console.error(`[${requestId}] Error checking existing tag:`, checkError);
        throw checkError;
      }

      if (existingTag) {
        return res.status(409).json({
          error: 'A tag with this name already exists',
          requestId
        });
      }

      // Create the tag
      const { data, error } = await supabaseAdmin
        .from('customer_tags')
        .insert({
          name: name.trim(),
          description: description?.trim() || null,
          color: color || '#6e8efb',
          category: category || 'general',
          sort_order: parseInt(sortOrder) || 0,
          is_system: false,
          created_by: authResult.user?.id
        })
        .select()
        .single();

      if (error) {
        console.error(`[${requestId}] Error creating tag:`, error);
        throw error;
      }

      console.log(`[${requestId}] Successfully created tag: ${data.name}`);

      return res.status(201).json({
        tag: data,
        requestId
      });

    } else if (req.method === 'PUT') {
      // PUT - Update existing tag
      const { id } = req.query;
      const { 
        name, 
        description, 
        color, 
        category,
        sortOrder
      } = req.body;

      if (!id) {
        return res.status(400).json({ 
          error: 'Tag ID is required',
          requestId 
        });
      }

      console.log(`[${requestId}] Updating tag: ${id}`);

      // Check if tag exists and is not a system tag
      const { data: existingTag, error: checkError } = await supabaseAdmin
        .from('customer_tags')
        .select('id, is_system')
        .eq('id', id)
        .single();

      if (checkError) {
        console.error(`[${requestId}] Error checking tag:`, checkError);
        if (checkError.code === 'PGRST116') {
          return res.status(404).json({
            error: 'Tag not found',
            requestId
          });
        }
        throw checkError;
      }

      if (existingTag.is_system) {
        return res.status(403).json({
          error: 'Cannot modify system tags',
          requestId
        });
      }

      // Prepare update data
      const updateData = {};
      if (name && name.trim()) updateData.name = name.trim();
      if (description !== undefined) updateData.description = description?.trim() || null;
      if (color) updateData.color = color;
      if (category) updateData.category = category;
      if (sortOrder !== undefined) updateData.sort_order = parseInt(sortOrder) || 0;
      updateData.updated_at = new Date().toISOString();

      // Update the tag
      const { data, error } = await supabaseAdmin
        .from('customer_tags')
        .update(updateData)
        .eq('id', id)
        .select()
        .single();

      if (error) {
        console.error(`[${requestId}] Error updating tag:`, error);
        throw error;
      }

      console.log(`[${requestId}] Successfully updated tag: ${data.name}`);

      return res.status(200).json({
        tag: data,
        requestId
      });

    } else if (req.method === 'DELETE') {
      // DELETE - Delete tag
      const { id } = req.query;

      if (!id) {
        return res.status(400).json({ 
          error: 'Tag ID is required',
          requestId 
        });
      }

      console.log(`[${requestId}] Deleting tag: ${id}`);

      // Check if tag exists and is not a system tag
      const { data: existingTag, error: checkError } = await supabaseAdmin
        .from('customer_tags')
        .select('id, is_system, name')
        .eq('id', id)
        .single();

      if (checkError) {
        console.error(`[${requestId}] Error checking tag:`, checkError);
        if (checkError.code === 'PGRST116') {
          return res.status(404).json({
            error: 'Tag not found',
            requestId
          });
        }
        throw checkError;
      }

      if (existingTag.is_system) {
        return res.status(403).json({
          error: 'Cannot delete system tags',
          requestId
        });
      }

      // Delete the tag (assignments will be deleted automatically due to CASCADE)
      const { error } = await supabaseAdmin
        .from('customer_tags')
        .delete()
        .eq('id', id);

      if (error) {
        console.error(`[${requestId}] Error deleting tag:`, error);
        throw error;
      }

      console.log(`[${requestId}] Successfully deleted tag: ${existingTag.name}`);

      return res.status(200).json({
        success: true,
        message: 'Tag deleted successfully',
        requestId
      });

    } else {
      return res.status(405).json({ 
        error: 'Method not allowed',
        requestId 
      });
    }

  } catch (error) {
    console.error(`[${requestId}] Customer tags API error:`, error);
    
    return res.status(500).json({
      error: 'Failed to process tag request',
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while processing your request',
      requestId,
      timestamp: new Date().toISOString()
    });
  }
}
