import { supabaseAdmin } from '@/lib/supabase';
import * as authTokenManager from '@/lib/auth-token-manager';

/**
 * Customer Tag Assignments API Endpoint
 * Handles tag assignments for individual customers
 */
export default async function handler(req, res) {
  // Generate a unique request ID for tracking
  const requestId = Math.random().toString(36).substring(2, 8);
  console.log(`[${requestId}] Customer tag assignments API: ${req.method} ${req.url}`);

  // Authenticate request using the auth token manager
  const authResult = await authTokenManager.verifyToken(req);

  if (!authResult.valid) {
    console.error(`[${requestId}] Authentication failed:`, authResult.error);
    return res.status(401).json({
      error: 'Unauthorized access',
      message: authResult.error || 'Authentication failed',
      requestId
    });
  }

  console.log(`[${requestId}] Authentication successful. User: ${authResult.user?.email}`);

  const { id: customerId } = req.query;

  if (!customerId) {
    return res.status(400).json({
      error: 'Customer ID is required',
      requestId
    });
  }

  try {
    if (req.method === 'GET') {
      // GET - Get customer's tags
      console.log(`[${requestId}] Fetching tags for customer: ${customerId}`);

      const { data, error } = await supabaseAdmin
        .from('customer_tag_assignments')
        .select(`
          id,
          assigned_at,
          notes,
          customer_tags (
            id,
            name,
            description,
            color,
            category,
            is_system
          )
        `)
        .eq('customer_id', customerId)
        .order('assigned_at', { ascending: false });

      if (error) {
        console.error(`[${requestId}] Error fetching customer tags:`, error);
        throw error;
      }

      // Transform the data to flatten the tag information
      const tags = data?.map(assignment => ({
        assignmentId: assignment.id,
        assignedAt: assignment.assigned_at,
        notes: assignment.notes,
        ...assignment.customer_tags
      })) || [];

      console.log(`[${requestId}] Successfully fetched ${tags.length} tags for customer`);

      return res.status(200).json({
        tags,
        requestId
      });

    } else if (req.method === 'POST') {
      // POST - Assign tag to customer
      console.log(`[${requestId}] Assigning tag to customer: ${customerId}`);

      const { tagId, notes } = req.body;

      if (!tagId) {
        return res.status(400).json({
          error: 'Tag ID is required',
          requestId
        });
      }

      // Check if customer exists
      const { data: customer, error: customerError } = await supabaseAdmin
        .from('customers')
        .select('id')
        .eq('id', customerId)
        .single();

      if (customerError) {
        console.error(`[${requestId}] Error checking customer:`, customerError);
        if (customerError.code === 'PGRST116') {
          return res.status(404).json({
            error: 'Customer not found',
            requestId
          });
        }
        throw customerError;
      }

      // Check if tag exists
      const { data: tag, error: tagError } = await supabaseAdmin
        .from('customer_tags')
        .select('id, name')
        .eq('id', tagId)
        .single();

      if (tagError) {
        console.error(`[${requestId}] Error checking tag:`, tagError);
        if (tagError.code === 'PGRST116') {
          return res.status(404).json({
            error: 'Tag not found',
            requestId
          });
        }
        throw tagError;
      }

      // Check if assignment already exists
      const { data: existingAssignment, error: checkError } = await supabaseAdmin
        .from('customer_tag_assignments')
        .select('id')
        .eq('customer_id', customerId)
        .eq('tag_id', tagId)
        .single();

      if (checkError && checkError.code !== 'PGRST116') {
        console.error(`[${requestId}] Error checking existing assignment:`, checkError);
        throw checkError;
      }

      if (existingAssignment) {
        return res.status(409).json({
          error: 'Tag is already assigned to this customer',
          requestId
        });
      }

      // Create the assignment
      const { data, error } = await supabaseAdmin
        .from('customer_tag_assignments')
        .insert({
          customer_id: customerId,
          tag_id: tagId,
          assigned_by: authResult.user?.id,
          notes: notes?.trim() || null
        })
        .select(`
          id,
          assigned_at,
          notes,
          customer_tags (
            id,
            name,
            description,
            color,
            category,
            is_system
          )
        `)
        .single();

      if (error) {
        console.error(`[${requestId}] Error creating tag assignment:`, error);
        throw error;
      }

      // Transform the response
      const assignedTag = {
        assignmentId: data.id,
        assignedAt: data.assigned_at,
        notes: data.notes,
        ...data.customer_tags
      };

      console.log(`[${requestId}] Successfully assigned tag "${tag.name}" to customer`);

      return res.status(201).json({
        tag: assignedTag,
        requestId
      });

    } else if (req.method === 'DELETE') {
      // DELETE - Remove tag from customer
      const { tagId } = req.query;

      if (!tagId) {
        return res.status(400).json({
          error: 'Tag ID is required',
          requestId
        });
      }

      console.log(`[${requestId}] Removing tag ${tagId} from customer: ${customerId}`);

      // Check if assignment exists
      const { data: assignment, error: checkError } = await supabaseAdmin
        .from('customer_tag_assignments')
        .select('id')
        .eq('customer_id', customerId)
        .eq('tag_id', tagId)
        .single();

      if (checkError) {
        console.error(`[${requestId}] Error checking assignment:`, checkError);
        if (checkError.code === 'PGRST116') {
          return res.status(404).json({
            error: 'Tag assignment not found',
            requestId
          });
        }
        throw checkError;
      }

      // Delete the assignment
      const { error } = await supabaseAdmin
        .from('customer_tag_assignments')
        .delete()
        .eq('customer_id', customerId)
        .eq('tag_id', tagId);

      if (error) {
        console.error(`[${requestId}] Error deleting tag assignment:`, error);
        throw error;
      }

      console.log(`[${requestId}] Successfully removed tag from customer`);

      return res.status(200).json({
        success: true,
        message: 'Tag removed from customer successfully',
        requestId
      });

    } else {
      return res.status(405).json({
        error: 'Method not allowed',
        requestId
      });
    }

  } catch (error) {
    console.error(`[${requestId}] Customer tag assignments API error:`, error);
    
    return res.status(500).json({
      error: 'Failed to process tag assignment request',
      message: process.env.NODE_ENV === 'development' ? error.message : 'An error occurred while processing your request',
      requestId,
      timestamp: new Date().toISOString()
    });
  }
}
